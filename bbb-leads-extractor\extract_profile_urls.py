import os
import sys
import json
import requests
from lxml import etree
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor, as_completed

DENTAL_CATEGORIES = {
    "dental-surgery", "cosmetic-dentistry", "children-dentist",
    "dental-supplies", "dental-insurance", "dental-xrays",
    "dental-implants", "dental-hygienist", "dentist",
    "denturist", "dental", "dental-services",
    "dental-laboratory", "dental-sedation", "dental-plans"
}

def extract_and_filter_business_data(sitemap_url, categories):
    """
    Downloads and parses a sitemap, filtering business data by category.
    It extracts both the URL and its last modified date.

    Args:
        sitemap_url (str): The URL of the sitemap XML file.
        categories (set): A set of category strings to filter by.

    Returns:
        list: A list of dictionaries, where each dict contains a matching
              'url' and its 'last_modified' date. Returns an empty list on error.
    """
    print(f"Processing sitemap: {sitemap_url}")
    try:
        response = requests.get(sitemap_url, timeout=30)
        response.raise_for_status()

        root = etree.fromstring(response.content)
        namespace = {'sitemap': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
        
        # This XPath finds all <url> elements in the sitemap
        url_elements = root.xpath('//sitemap:url', namespaces=namespace)
        
        filtered_items = []
        for url_element in url_elements:
            # For each <url>, find its <loc> and <lastmod> children
            loc = url_element.findtext('sitemap:loc', namespaces=namespace)
            if not loc:
                continue

            # Efficiently check if the URL's path contains a target category
            if any(f"/{cat}/" in loc for cat in categories):
                last_mod = url_element.findtext('sitemap:lastmod', namespaces=namespace)
                filtered_items.append({
                    "url": loc,
                    "last_modified": last_mod
                })
        
        print(f"-> Found {len(filtered_items)} matching items in {sitemap_url}")
        return filtered_items

    except requests.exceptions.RequestException as e:
        print(f"Error fetching {sitemap_url}: {e}", file=sys.stderr)
    except etree.XMLSyntaxError as e:
        print(f"Error parsing XML from {sitemap_url}: {e}", file=sys.stderr)
        
    return []

def save_json_to_file(data, output_file):
    """Saves a dictionary to a JSON file with pretty printing."""
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except (IOError, TypeError) as e:
        print(f"Error saving data to JSON file {output_file}: {e}", file=sys.stderr)
        return False

# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
#
# PRODUCTION MODE: Process ALL sitemaps concurrently.
#
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
if __name__ == "__main__":
    input_file = os.path.join("data", "bbb_sitemap.json")
    output_file = os.path.join("data", "filtered_dental_data_ALL.json")
    job_timestamp = datetime.now(timezone.utc).isoformat()

    # --- Step 1: Read the sitemap data from the input JSON file ---
    if not os.path.exists(input_file):
        print(f"Error: Input file not found at {input_file}", file=sys.stderr)
        sys.exit(1)
        
    try:
        with open(input_file, 'r') as f:
            sitemap_data = json.load(f)
        sitemap_locations = [item['loc'] for item in sitemap_data.get('sitemaps', [])]
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Error reading or parsing JSON file {input_file}: {e}", file=sys.stderr)
        sys.exit(1)

    if not sitemap_locations:
        print("No sitemap locations found in the input file.", file=sys.stderr)
        sys.exit(1)

    # --- Step 2: Process ALL sitemaps concurrently for high performance ---
    print(f"--- Running in production mode: processing all {len(sitemap_locations)} sitemaps ---")
    all_matching_items = {} # Use a dictionary to automatically handle duplicates

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_url = {
            executor.submit(extract_and_filter_business_data, url, DENTAL_CATEGORIES): url 
            for url in sitemap_locations
        }
        
        for future in as_completed(future_to_url):
            try:
                # The result is a list of dictionaries
                result_items = future.result()
                for item in result_items:
                    # By using the URL as a key, we keep only the last-seen entry for any duplicates.
                    all_matching_items[item['url']] = item
            except Exception as e:
                url = future_to_url[future]
                print(f"An exception occurred processing future for {url}: {e}", file=sys.stderr)

    # --- Step 3: Format and save the combined results ---
    final_items = list(all_matching_items.values())
    output_data = {
        "scrapping_job_start_timestamp": job_timestamp,
        "items": final_items
    }
    
    if final_items:
        print(f"\nFound a total of {len(final_items)} unique matching items across all sitemaps.")
        if save_json_to_file(output_data, output_file):
            print(f"✅ Success! All matching data saved to {output_file}")
        else:
            print("\n❌ Failed to save data to file.", file=sys.stderr)
    else:
        print("\nNo matching items were found across any of the sitemaps.")