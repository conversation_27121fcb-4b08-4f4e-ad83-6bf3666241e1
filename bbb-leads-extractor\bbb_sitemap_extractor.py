"""
This script extracts sitemap URLs and lastmod timestamps from a sitemap index,
and saves them to a structured JSON file with a generation timestamp.
"""

import os
import sys
import json
from datetime import datetime
import requests
from lxml import etree

def extract_sitemap_entries(sitemap_url):
    """
    Downloads a sitemap index and extracts all <loc> and <lastmod> pairs.

    Args:
        sitemap_url: The URL of the sitemap index file.

    Returns:
        A list of dicts, each with 'loc' and 'lastmod', or None if an error occurred.
    """
    try:
        response = requests.get(sitemap_url, timeout=10)
        response.raise_for_status()

        root = etree.fromstring(response.content)
        ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}

        entries = []
        for sm_el in root.xpath('//sm:sitemap', namespaces=ns):
            loc_el = sm_el.find('sm:loc', namespaces=ns)
            lm_el  = sm_el.find('sm:lastmod', namespaces=ns)
            if loc_el is not None and lm_el is not None:
                entries.append({
                    'loc': loc_el.text.strip(),
                    'lastmod': lm_el.text.strip()
                })
        return entries

    except requests.exceptions.RequestException as e:
        print(f"Error fetching the sitemap: {e}", file=sys.stderr)
    except etree.XMLSyntaxError as e:
        print(f"Error parsing the XML: {e}", file=sys.stderr)

    return None

def save_json(data, output_file):
    """
    Saves the provided data as JSON.

    Args:
        data: A Python object (typically dict) to serialize.
        output_file: Path to the output JSON file.

    Returns:
        True if successful, False otherwise.
    """
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving JSON to file: {e}", file=sys.stderr)
        return False

if __name__ == "__main__":
    sitemap_url = "https://www.bbb.org/sitemap-business-profiles-index.xml"
    output_file = os.path.join("data", "bbb_sitemap.json")

    print(f"Extracting entries from {sitemap_url}...")
    entries = extract_sitemap_entries(sitemap_url)

    if entries is None:
        print("Failed to extract any entries.", file=sys.stderr)
        sys.exit(1)

    payload = {
        'generated_at': datetime.now().isoformat(),
        'sitemaps': entries
    }

    print(f"Found {len(entries)} entries. Writing to {output_file}...")
    if save_json(payload, output_file):
        print("JSON successfully saved.")
    else:
        sys.exit(1)
