import re
import json
import time 
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup

class BBBProfileExtractor:
   def __init__(self):
       self.skip_domains = frozenset([
           'facebook', 'twitter', 'linkedin', 'instagram', 'youtube', 'bbb.org',
           'bbbprograms.org', 'give.org', 'google.com', 'maps.google',
           'googletagmanager', 'mouseflow.com', 'cloudflareinsights', 'e2ma.net',
           'mailchimp.com', 'constantcontact.com', 'signup.', 'newsletter.',
           'livechatinc.com', 'subscribe.'
       ])
       self.phone_pattern = re.compile(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}')
       self.zip_pattern = re.compile(r'(\d{5})(?:-(\d{4}))?')
       self.state_pattern = re.compile(r'\b([A-Z]{2})\b')
       self.whitespace_pattern = re.compile(r'\s+')

   def extract_profile(self, html_content: str) -> Dict[str, Any]:
       """Extract business profile from HTML content with comprehensive error handling"""
       if not html_content or not html_content.strip():
           raise ValueError("HTML content cannot be empty")
       
       try:
           soup = BeautifulSoup(html_content, 'html.parser')
       except Exception as e:
           raise ValueError(f"Failed to parse HTML: {e}")

       data = self._initialize_data()
       self._extract_from_html(soup, data)
       self._clean_data(data)
       return data

   def _initialize_data(self) -> Dict[str, Any]:
       """Initialize data dictionary with default values"""
       return {
           "id": str(uuid.uuid4()), "dso_id": None, "name": None, "street": None,
           "city": None, "state": None, "zip": None, "zipcode_extended": None,
           "phone": None, "email": None, "website": None, "date_opened": None,
           "status": "Active", "bbb_rating": None, "local_bbb": None,
           "bbb_file_opened": None, "business_started": None,
           "business_started_locally": None, "business_incorporated": None,
           "type_of_entity": None, "business_management": None,
           "created_at": datetime.now().isoformat()
       }

   def _extract_from_html(self, soup: BeautifulSoup, data: Dict[str, Any]):
       """Extract all data fields from parsed HTML soup"""
       business_name_elem = soup.find(id='businessName')
       if business_name_elem:
           data['name'] = business_name_elem.get_text(strip=True)

       phone_link = soup.find('a', href=re.compile(r'^tel:'))
       if phone_link:
           data['phone'] = self._clean_phone(phone_link.get_text(strip=True))

       data['website'] = self._extract_website(soup)
       data['bbb_rating'] = self._extract_rating(soup)
       self._extract_address(soup, data)
       self._extract_business_details(soup, data)

   def _extract_website(self, soup: BeautifulSoup) -> Optional[str]:
       """Find and return website URL from various potential locations"""
       website_link = (soup.find('a', string=re.compile(r'Visit Website', re.I)) or
                      soup.find('a', string=lambda text: text and 'Visit Website' in text))
       
       if not website_link:
           header_contact = soup.find(class_='bpr-header-contact')
           if header_contact:
               for link in header_contact.find_all('a', href=True):
                   href = link.get('href', '')
                   if (href.startswith('http') and 
                       not any(domain in href.lower() for domain in self.skip_domains)):
                       return href
       
       if website_link:
           href = website_link.get('href', '')
           if href and not any(domain in href.lower() for domain in self.skip_domains):
               return href
       return None

   def _extract_rating(self, soup: BeautifulSoup) -> Optional[str]:
       """Extract BBB rating or not-rated status"""
       rating_elem = soup.find(class_='bpr-letter-grade')
       if rating_elem:
           return rating_elem.get_text(strip=True)
       not_rated_elem = soup.find(class_='bpr-not-rated')
       return not_rated_elem.get_text(strip=True) if not_rated_elem else None

   def _extract_address(self, soup: BeautifulSoup, data: Dict[str, Any]):
       """Parse address components from overview section"""
       overview_address = soup.find(class_='bpr-overview-address')
       if not overview_address:
           return

       address_lines = overview_address.find_all('p', class_='bds-body')
       if len(address_lines) >= 2:
           data['street'] = address_lines[0].get_text(strip=True)
           self._parse_location(address_lines[1].get_text(strip=True), data)

   def _parse_location(self, location_text: str, data: Dict[str, Any]):
       """Parse city, state, and ZIP from location string"""
       location_text = self.whitespace_pattern.sub(' ', location_text.strip())

       zip_match = self.zip_pattern.search(location_text)
       if zip_match:
           data['zip'] = zip_match.group(1)
           data['zipcode_extended'] = (f"{zip_match.group(1)}-{zip_match.group(2)}" 
                                     if zip_match.group(2) else zip_match.group(1))
           location_text = location_text[:zip_match.start()].strip()

       state_match = self.state_pattern.search(location_text)
       if state_match:
           data['state'] = state_match.group(1)
           location_text = location_text[:state_match.start()].strip()

       if location_text:
           data['city'] = location_text.rstrip(',').strip()

   def _extract_business_details(self, soup: BeautifulSoup, data: Dict[str, Any]):
       """Extract business details from definition list elements"""
       detail_mapping = {
           'Local BBB': 'local_bbb',
           'BBB File Opened': 'bbb_file_opened',
           'Business Started': ['business_started', 'business_started_locally', 'date_opened'],
           'Business Started Locally': 'business_started_locally',
           'Type of Entity': 'type_of_entity',
           'Entity Type': 'type_of_entity',
           'Business Type': 'type_of_entity',
           'Business Management': 'business_management',
           'Business Incorporated': 'business_incorporated'
       }

       for element in soup.find_all(class_='bpr-details-dl-data'):
           dt, dd = element.find('dt'), element.find('dd')
           if not (dt and dd):
               continue

           key = dt.get_text(strip=True).replace(':', '')
           value = dd.get_text(separator='\n', strip=True)

           if key in detail_mapping:
               mapping = detail_mapping[key]
               if isinstance(mapping, list):
                   for field in mapping:
                       data[field] = value
               else:
                   data[mapping] = value
           elif key in ['Principal Contacts', 'Customer Contacts'] and not data['business_management']:
               data['business_management'] = value

   def _clean_phone(self, phone_text: str) -> str:
       """Extract and clean phone number from text"""
       match = self.phone_pattern.search(phone_text)
       return match.group(0) if match else phone_text

   def _clean_data(self, data: Dict[str, Any]):
       """Clean and normalize extracted data fields"""
       if data.get('name'):
           data['name'] = data['name'].split('|')[0].strip()

       if data.get('phone'):
           data['phone'] = self._clean_phone(data['phone'])

       if data.get('website'):
           website = data['website'].strip()
           if not website.startswith(('http://', 'https://')):
               if website.startswith('www.') or '.' in website:
                   website = f"http://{website}"
           data['website'] = website

       for field in ['street', 'city', 'state', 'zip', 'zipcode_extended']:
           if data.get(field):
               data[field] = str(data[field]).strip() or None

def extract_from_file(file_path: str, output_file: Optional[str] = None) -> Dict[str, Any]:
   """Extract business data from single HTML file"""
   if not file_path:
       raise ValueError("File path cannot be empty")

   try:
       with open(file_path, 'r', encoding='utf-8') as f:
           html_content = f.read()
   except (FileNotFoundError, PermissionError, UnicodeDecodeError) as e:
       raise ValueError(f"Error reading file {file_path}: {e}")

   extractor = BBBProfileExtractor()
   business_data = extractor.extract_profile(html_content)

   if output_file:
       try:
           with open(output_file, 'w', encoding='utf-8') as f:
               json.dump(business_data, f, indent=4, ensure_ascii=False)
       except (PermissionError, OSError) as e:
           raise ValueError(f"Error writing to {output_file}: {e}")

   return business_data

def process_directory(directory_path: str, output_file: Optional[str] = None) -> List[Dict[str, Any]]:
   """Process all HTML files in directory and return extracted data"""
   import os

   if not os.path.exists(directory_path):
       raise ValueError(f"Directory not found: {directory_path}")
   if not os.path.isdir(directory_path):
       raise ValueError(f"Path is not a directory: {directory_path}")

   try:
       html_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.html')]
   except PermissionError as e:
       raise ValueError(f"Permission denied accessing directory: {e}")

   if not html_files:
       raise ValueError(f"No HTML files found in directory: {directory_path}")

   all_data = []
   extractor = BBBProfileExtractor()

   for html_file in html_files:
       file_path = os.path.join(directory_path, html_file)
       try:
           with open(file_path, 'r', encoding='utf-8') as f:
               html_content = f.read()
           all_data.append(extractor.extract_profile(html_content))
       except Exception as e:
           print(f"Error processing {html_file}: {e}")
           continue

   if output_file and all_data:
       try:
           with open(output_file, 'w', encoding='utf-8') as f:
               json.dump(all_data, f, indent=4, ensure_ascii=False)
       except (PermissionError, OSError) as e:
           raise ValueError(f"Error writing to {output_file}: {e}")

   return all_data

def main():
   """Main execution function for single file processing"""
   try:
#NOTE: CHANGE THE PATH ACCORDIDNG TO YOUR LOCAL DIRECTORY
       input_html_path = r"dataset-small/1-800-dentist-1216-13054010.html"
       output_json_path = 'profile_data_v3.json'
       
       extracted_data = extract_from_file(input_html_path, output_json_path)
       print(json.dumps(extracted_data, indent=2, ensure_ascii=False))
        
#NOTE: Uncomment below to process entire directory
#         start_time = time.time()
#         all_data = process_directory('dataset-small', 'all_profiles_v3_OPTIMIZED.json')
#         print(f"\nProcessed {len(all_data)} files total")
#         end_time = time.time()
#         print(f"\nTotal execution time: {end_time - start_time:.2f} seconds.")
   except Exception as e:
       print(f"Error: {e}")

if __name__ == "__main__":
   main()