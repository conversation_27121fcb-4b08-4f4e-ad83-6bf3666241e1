import re
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any
from bs4 import BeautifulSoup

class BBBProfileExtractorV3:
    def __init__(self):
        self.skip_domains = [
            'facebook', 'twitter', 'linkedin', 'instagram', 'youtube', 'bbb.org',
            'bbbprograms.org', 'give.org', 'google.com', 'maps.google',
            'googletagmanager', 'mouseflow.com', 'cloudflareinsights', 'e2ma.net',
            'mailchimp.com', 'constantcontact.com', 'signup.', 'newsletter.',
            'livechatinc.com', 'subscribe.'
        ]

    def extract_profile(self, html_content: str) -> Dict[str, Any]:
        if not html_content or not html_content.strip():
            raise ValueError("HTML content cannot be empty")

        soup = BeautifulSoup(html_content, 'html.parser')

        data = {
            "id": str(uuid.uuid4()),
            "dso_id": None,
            "name": None,
            "street": None,
            "city": None,
            "state": None,
            "zip": None,
            "zipcode_extended": None,
            "phone": None,
            "email": None,
            "website": None,
            "date_opened": None,
            "status": "Active",
            "bbb_rating": None,
            "local_bbb": None,
            "bbb_file_opened": None,
            "business_started": None,
            "business_started_locally": None,
            "business_incorporated": None,
            "type_of_entity": None,
            "business_management": None,
            "created_at": datetime.now().isoformat()
        }

        self._extract_from_html(soup, data)
        self._clean_data(data)

        return data

    def _extract_from_html(self, soup: BeautifulSoup, data: Dict[str, Any]):
        # Extract business name from businessName id
        business_name_elem = soup.find(id='businessName')
        if business_name_elem:
            data['name'] = business_name_elem.get_text(strip=True)

        # Extract phone from tel: links
        phone_link = soup.find('a', href=re.compile(r'^tel:'))
        if phone_link:
            phone_text = phone_link.get_text(strip=True)
            data['phone'] = self._clean_phone(phone_text)

        # Extract website from Visit Website link
        website_link = soup.find('a', string=re.compile(r'Visit Website', re.I))
        if not website_link:
            # Try to find link with "Visit Website" text inside
            website_link = soup.find('a', string=lambda text: text and 'Visit Website' in text)
        if not website_link:
            # Try to find any external link that's not in skip domains
            # Look specifically in the header contact section first
            header_contact = soup.find(class_='bpr-header-contact')
            if header_contact:
                contact_links = header_contact.find_all('a', href=True)
                for link in contact_links:
                    href = link.get('href', '')
                    if (href.startswith('http') and
                        not any(domain in href.lower() for domain in self.skip_domains) and
                        'bbb.org' not in href.lower()):
                        website_link = link
                        break

        if website_link:
            href = website_link.get('href', '')
            if href and not any(domain in href.lower() for domain in self.skip_domains):
                data['website'] = href

        # Extract BBB rating
        rating_elem = soup.find(class_='bpr-letter-grade')
        if rating_elem:
            data['bbb_rating'] = rating_elem.get_text(strip=True)
        else:
            # Check for "Not Rated" case
            not_rated_elem = soup.find(class_='bpr-not-rated')
            if not_rated_elem:
                data['bbb_rating'] = not_rated_elem.get_text(strip=True)

        # Extract address from overview section
        self._extract_address(soup, data)

        # Extract business details
        self._extract_business_details(soup, data)

    def _extract_address(self, soup: BeautifulSoup, data: Dict[str, Any]):
        # Look for address in the overview card
        overview_address = soup.find(class_='bpr-overview-address')
        if overview_address:
            address_lines = overview_address.find_all('p', class_='bds-body')
            if len(address_lines) >= 2:
                # First line is street
                data['street'] = address_lines[0].get_text(strip=True)

                # Second line contains city, state, zip
                location_text = address_lines[1].get_text(strip=True)
                self._parse_location(location_text, data)

    def _parse_location(self, location_text: str, data: Dict[str, Any]):
        # Parse "City, State ZIP" or "City, State ZIP-EXTENDED"
        location_text = re.sub(r'\s+', ' ', location_text.strip())

        # Extract ZIP code
        zip_match = re.search(r'(\d{5})(?:-(\d{4}))?', location_text)
        if zip_match:
            data['zip'] = zip_match.group(1)
            data['zipcode_extended'] = f"{zip_match.group(1)}-{zip_match.group(2)}" if zip_match.group(2) else zip_match.group(1)
            location_text = location_text[:zip_match.start()].strip()

        # Extract state (2 letter code)
        state_match = re.search(r'\b([A-Z]{2})\b', location_text)
        if state_match:
            data['state'] = state_match.group(1)
            location_text = location_text[:state_match.start()].strip()

        # Remaining text is city (remove trailing comma)
        if location_text:
            data['city'] = location_text.rstrip(',').strip()

    def _extract_business_details(self, soup: BeautifulSoup, data: Dict[str, Any]):
        # Find all business detail data elements
        detail_elements = soup.find_all(class_='bpr-details-dl-data')

        for element in detail_elements:
            dt = element.find('dt')
            dd = element.find('dd')

            if dt and dd:
                key = dt.get_text(strip=True).replace(':', '')
                value = dd.get_text(separator='\n', strip=True)

                # Map the keys to our data structure
                if key == 'Local BBB':
                    data['local_bbb'] = value
                elif key == 'BBB File Opened':
                    data['bbb_file_opened'] = value
                elif key == 'Business Started':
                    data['business_started'] = value
                    data['business_started_locally'] = value
                    data['date_opened'] = value
                elif key == 'Business Started Locally':
                    data['business_started_locally'] = value
                elif key == 'Type of Entity' or key == 'Entity Type' or key == 'Business Type':
                    data['type_of_entity'] = value
                elif key == 'Business Management':
                    data['business_management'] = value
                elif key in ['Principal Contacts', 'Customer Contacts'] and not data['business_management']:
                    data['business_management'] = value
                elif key == 'Business Incorporated':
                    data['business_incorporated'] = value

    def _clean_phone(self, phone_text: str) -> str:
        # Extract phone number pattern
        match = re.search(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', phone_text)
        return match.group(0) if match else phone_text

    def _clean_data(self, data: Dict[str, Any]):
        # Clean business name
        if data.get('name'):
            data['name'] = data['name'].split('|')[0].strip()

        # Clean phone
        if data.get('phone'):
            data['phone'] = self._clean_phone(data['phone'])

        # Clean website
        if data.get('website'):
            website = data['website'].strip()
            if not website.startswith(('http://', 'https://')):
                if website.startswith('www.') or '.' in website:
                    website = f"http://{website}"
            data['website'] = website

        # Clean address fields
        for field in ['street', 'city', 'state', 'zip', 'zipcode_extended']:
            if data.get(field):
                data[field] = str(data[field]).strip() if data[field] else None


def extract_from_file(file_path: str, output_file: str = None) -> Dict[str, Any]:
    if not file_path:
        raise ValueError("File path cannot be empty")

    with open(file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    extractor = BBBProfileExtractorV3()
    business_data = extractor.extract_profile(html_content)

    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(business_data, f, indent=4, ensure_ascii=False)
        print(f"Data successfully extracted and saved to {output_file}")

    return business_data

def process_directory(directory_path: str, output_file: str = None) -> list:
    import os

    if not os.path.exists(directory_path):
        raise ValueError(f"Directory not found: {directory_path}")

    html_files = [f for f in os.listdir(directory_path) if f.endswith('.html')]
    if not html_files:
        raise ValueError(f"No HTML files found in directory: {directory_path}")

    all_data = []
    extractor = BBBProfileExtractorV3()

    for html_file in html_files:
        file_path = os.path.join(directory_path, html_file)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            business_data = extractor.extract_profile(html_content)
            all_data.append(business_data)
            print(f"✓ Processed: {html_file}")\

        except Exception as e:
            print(f"✗ Error processing {html_file}: {e}")

    if output_file and all_data:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, indent=4, ensure_ascii=False)
        print(f"\nAll data saved to {output_file}")

    return all_data

def main():
    try:
        # Single file processing
        # input_html_path = r"dataset-small/1st-eye-care-0825-1000223083.html"
        # output_json_path = 'profile_data_v3.json'

        # extracted_data = extract_from_file(input_html_path, output_json_path)
        # print("\n--- Extracted Data ---")
        # print(json.dumps(extracted_data, indent=2, ensure_ascii=False))

        # Uncomment below to process entire directory
        start_time = time.time()
        all_data = process_directory('dataset-small', 'all_profiles_v3.json')
        print(f"\nProcessed {len(all_data)} files total")
        end_time = time.time()
        print(f"\nTotal execution time: {end_time - start_time:.2f} seconds.")

    except FileNotFoundError:
        print(f"Error: Input file not found. Please check the path.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()