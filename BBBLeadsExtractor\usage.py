import os
import json
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
from unifiedExtractor import UnifiedBBBExtractor

def process_file_worker(file_path: str) :
    """
    A worker function that processes a single HTML file.
    It's designed to be called by a ProcessPoolExecutor.
    Returns the file path, the result, or an error message.
    """
    try:
        extractor = UnifiedBBBExtractor()
        result = extractor.process_html_file(file_path)
        return file_path, result, None
    except Exception as e:
        return file_path, None, str(e)

def main(input_dir: str, output_dir: str):
    """
    Main function to process a directory of HTML files concurrently.
    """
    if not os.path.isdir(input_dir):
        print(f"Error: Input directory not found at '{input_dir}'")
        return

    os.makedirs(output_dir, exist_ok=True)

    html_files = [os.path.join(input_dir, f) for f in os.listdir(input_dir) if f.endswith(('.html', '.htm'))]
    
    if not html_files:
        print(f"No HTML files found in '{input_dir}'")
        return

    all_dso_parents = []
    all_dds_and_branches = []
    
    print(f"Starting to process {len(html_files)} HTML files using multiple cores...")

    with ProcessPoolExecutor() as executor:
        # Submit all files to the process pool
        futures = {executor.submit(process_file_worker, file_path): file_path for file_path in html_files}
        
        # Process results as they are completed
        for future in tqdm(as_completed(futures), total=len(html_files), desc="Processing HTML files"):
            file_path, result, error = future.result()
            
            if error:
                print(f"\n--- ERROR ---")
                print(f"Failed to process file: {os.path.basename(file_path)}")
                print(f"Reason: {error}")
                print(f"-------------")
                continue

            if result:
                # If len > 1, it's a DSO. First item is the parent, rest are branches.
                if len(result) > 1:
                    all_dso_parents.append(result[0])
                    all_dds_and_branches.extend(result[1:])
                # If len == 1, it's a single practice (DDS).
                else:
                    all_dds_and_branches.extend(result)

    # --- Write the final JSON files ---
    dso_output_path = os.path.join(output_dir, 'dsos.json')
    with open(dso_output_path, 'w', encoding='utf-8') as f:
        json.dump(all_dso_parents, f, indent=4, ensure_ascii=False)
    print(f"\nSuccessfully extracted {len(all_dso_parents)} DSO parent records.")
    print(f"DSO data saved to: {dso_output_path}")

    dds_output_path = os.path.join(output_dir, 'dds_and_branches.json')
    with open(dds_output_path, 'w', encoding='utf-8') as f:
        json.dump(all_dds_and_branches, f, indent=4, ensure_ascii=False)
    print(f"\nSuccessfully extracted {len(all_dds_and_branches)} DDS and branch records.")
    print(f"DDS and branch data saved to: {dds_output_path}")


if __name__ == '__main__':
    # --- Setup Command-Line Argument Parsing ---
    parser = argparse.ArgumentParser(
        description="Concurrently process a directory of BBB HTML profile files and extract DSO and DDS information.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '-i', '--input_dir', 
        type=str, 
        required=True, 
        help="Directory containing the HTML files to process."
    )
    parser.add_argument(
        '-o', '--output_dir', 
        type=str, 
        required=True, 
        help="Directory where the output JSON files ('dsos.json' and 'dds_and_branches.json') will be saved."
    )
    
    args = parser.parse_args()
    
    # --- Run the Main Processing Function ---
    # Example usage:
    # python usage.py -i /path/to/your/html_files -o /path/to/your/output
    main(args.input_dir, args.output_dir)